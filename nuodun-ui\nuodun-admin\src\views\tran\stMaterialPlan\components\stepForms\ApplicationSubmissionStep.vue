<template>
  <div class="application-submission-step">
    <!-- 查看模式 -->
    <div v-if="readonly" class="view-mode">
      <div class="step-info">
        <h4>申请递交确认详情</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>客户确认状态：</label>
            <el-tag :type="stepData.customerConfirmed ? 'success' : 'warning'">
              {{ stepData.customerConfirmed ? '已确认递交' : '待客户确认' }}
            </el-tag>
          </div>
          <div v-if="stepData.customerConfirmed" class="info-item">
            <label>递交时间：</label>
            <span>{{ parseTime(stepData.submissionTime) || '暂无' }}</span>
          </div>
          <div v-if="stepData.trackingNumber" class="info-item">
            <label>申请追踪号：</label>
            <span>{{ stepData.trackingNumber }}</span>
          </div>
          <div class="info-item">
            <label>递交状态：</label>
            <el-tag :type="getSubmissionStatusType(stepData.submissionStatus)">
              {{ getSubmissionStatusText(stepData.submissionStatus) }}
            </el-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑模式 -->
    <div v-else class="edit-mode">
      <el-form :model="stepData" :rules="rules" ref="formRef" label-width="140px">
        <el-form-item label="客户确认状态" prop="customerConfirmed">
          <el-radio-group v-model="stepData.customerConfirmed">
            <el-radio :label="true">已确认递交</el-radio>
            <el-radio :label="false">待客户确认</el-radio>
          </el-radio-group>
          <div class="form-tip">
            <el-text type="warning" size="small">
              注意：客户确认后将正式递交申请到学校，请确认所有信息无误
            </el-text>
          </div>
        </el-form-item>

        <template v-if="stepData.customerConfirmed">
          <el-form-item label="递交时间" prop="submissionTime">
            <el-date-picker
              v-model="stepData.submissionTime"
              type="datetime"
              placeholder="选择递交时间"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>

          <el-form-item label="申请追踪号" prop="trackingNumber">
            <el-input v-model="stepData.trackingNumber" placeholder="请输入学校提供的申请追踪号" />
          </el-form-item>

          <el-form-item label="递交状态" prop="submissionStatus">
            <el-select v-model="stepData.submissionStatus" placeholder="请选择递交状态">
              <el-option label="已递交待处理" value="SUBMITTED" />
              <el-option label="学校已接收" value="RECEIVED" />
              <el-option label="材料审核中" value="REVIEWING" />
              <el-option label="递交失败" value="FAILED" />
            </el-select>
          </el-form-item>
        </template>

        <el-form-item label="备注说明" prop="remark">
          <el-input
            v-model="stepData.remark"
            type="textarea"
            :rows="3"
            placeholder="请描述递交过程中的注意事项或特殊情况"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { parseTime } from '@/utils/ruoyi'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const formRef = ref(null)

// 步骤数据
const stepData = reactive({
  customerConfirmed: false,
  submissionTime: '',
  trackingNumber: '',
  submissionStatus: '',
  remark: ''
})

// 验证规则
const rules = {
  customerConfirmed: [
    { required: true, message: '请选择客户确认状态', trigger: 'change' }
  ],
  submissionTime: [
    { required: true, message: '请选择递交时间', trigger: 'change', validator: (rule, value, callback) => {
      if (stepData.customerConfirmed && !value) {
        callback(new Error('请选择递交时间'))
      } else {
        callback()
      }
    }}
  ],
  submissionStatus: [
    { required: true, message: '请选择递交状态', trigger: 'change', validator: (rule, value, callback) => {
      if (stepData.customerConfirmed && !value) {
        callback(new Error('请选择递交状态'))
      } else {
        callback()
      }
    }}
  ]
}

// 获取递交状态类型
const getSubmissionStatusType = (status) => {
  const typeMap = {
    'SUBMITTED': 'primary',
    'RECEIVED': 'success',
    'REVIEWING': 'warning',
    'FAILED': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取递交状态文本
const getSubmissionStatusText = (status) => {
  const textMap = {
    'SUBMITTED': '已递交待处理',
    'RECEIVED': '学校已接收',
    'REVIEWING': '材料审核中',
    'FAILED': '递交失败'
  }
  return textMap[status] || '未设置'
}

// 初始化数据
const initData = () => {
  Object.assign(stepData, {
    customerConfirmed: false,
    submissionTime: '',
    trackingNumber: '',
    submissionStatus: '',
    remark: '',
    ...props.modelValue
  })
}

// 监听数据变化
watch(() => stepData, (newData) => {
  emit('update:modelValue', { ...newData })
  emit('change', { ...newData })
}, { deep: true })

// 监听props变化
watch(() => props.modelValue, () => {
  initData()
}, { immediate: true })

// 验证表单
const validate = async () => {
  if (!formRef.value) return true
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    return false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(stepData, {
    customerConfirmed: false,
    submissionTime: '',
    trackingNumber: '',
    submissionStatus: '',
    remark: ''
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

defineExpose({
  validate,
  resetForm
})
</script>

<style scoped lang="scss">
.application-submission-step {
  .view-mode {
    .step-info {
      h4 {
        margin-bottom: 16px;
        color: #303133;
        font-weight: 600;
      }

      .info-grid {
        display: grid;
        gap: 12px;

        .info-item {
          display: flex;
          align-items: flex-start;

          label {
            min-width: 120px;
            font-weight: 500;
            color: #606266;
          }

          span {
            color: #303133;
            word-break: break-all;
          }
        }
      }
    }
  }

  .edit-mode {
    .el-form {
      max-width: 600px;
    }

    .form-tip {
      margin-top: 8px;
      padding: 8px 12px;
      background: #fdf6ec;
      border: 1px solid #faecd8;
      border-radius: 4px;
    }
  }
}
</style> 