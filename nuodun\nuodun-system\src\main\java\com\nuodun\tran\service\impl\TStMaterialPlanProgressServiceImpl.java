package com.nuodun.tran.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nuodun.common.enums.CommonEnums;
import com.nuodun.common.enums.MaterialPlanProgressEnum;
import com.nuodun.common.enums.UserStatus;
import com.nuodun.common.utils.DateUtils;
import com.nuodun.common.utils.JposAssert;
import com.nuodun.tran.domain.TStMaterialPlanProgress;
import com.nuodun.tran.domain.dto.MaterialPlanProgressLogDto;
import com.nuodun.tran.domain.dto.UpdateProgressDto;
import com.nuodun.tran.mapper.TStMaterialPlanProgressMapper;
import com.nuodun.tran.service.TStMaterialPlanProgressLogService;
import com.nuodun.tran.service.TStMaterialPlanProgressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * 专业申请流程进度Service实现类
 * 提供申请进度的完整生命周期管理，包括初始化、状态更新、步骤流转等功能
 * 使用枚举类型确保数据一致性，使用DTO封装复杂参数提高代码可维护性
 *
 * <AUTHOR>
 * @description 针对表【t_st_material_plan_progress(专业申请流程进度表)】的数据库操作Service实现
 * @createDate 2025-07-21
 */
@Service
public class TStMaterialPlanProgressServiceImpl extends ServiceImpl<TStMaterialPlanProgressMapper, TStMaterialPlanProgress>
    implements TStMaterialPlanProgressService {

    @Autowired
    private TStMaterialPlanProgressLogService logService;

    @Override
    public TStMaterialPlanProgress getByPlanId(Long planId) {
        return lambdaQuery()
                .eq(TStMaterialPlanProgress::getPlanId, planId)
                .eq(TStMaterialPlanProgress::getDelFlag, UserStatus.OK.getCode())
                .one();
    }

    @Override
    public List<TStMaterialPlanProgress> getByStId(Long stId) {
        return lambdaQuery()
                .eq(TStMaterialPlanProgress::getStId, stId)
                .eq(TStMaterialPlanProgress::getDelFlag, UserStatus.OK.getCode())
                .orderByDesc(TStMaterialPlanProgress::getCreateTime)
                .list();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TStMaterialPlanProgress initProgress(Long planId, Long stId, Long contractId, String orderId,
                                                String stDecaType, String applySchool, String applyMajorZh, String applyMajorEn) {
        // 检查是否已存在进度记录
        TStMaterialPlanProgress existingProgress = getByPlanId(planId);
        if (existingProgress != null) {
            return existingProgress;
        }

        // 创建新的进度记录
        TStMaterialPlanProgress progress = new TStMaterialPlanProgress();
        progress.setPlanId(planId);
        progress.setStId(stId);
        progress.setContractId(contractId);
        progress.setOrderId(orderId);
        progress.setStDecaType(stDecaType);
        progress.setApplySchool(applySchool);
        progress.setApplyMajorZh(applyMajorZh);
        progress.setApplyMajorEn(applyMajorEn);

        // 设置初始状态 - 新的14步骤流程从"文书环节"开始
        progress.setCurrentStep("WRITING_PROCESS");
        progress.setCurrentStepName("文书环节");
        progress.setProgressStatus(MaterialPlanProgressEnum.ProgressStatus.IN_PROGRESS.getCode());

        // 初始化所有步骤状态为PENDING - 新的14步骤
        String pendingStatus = MaterialPlanProgressEnum.StepStatus.PENDING.getCode();
        
        // 第一阶段：申请准备阶段
        progress.setStepWritingProcessStatus(pendingStatus);
        progress.setStepWritingFinalizationStatus(pendingStatus);
        progress.setStepApplicationFillingStatus(pendingStatus);
        progress.setStepApplicationSubmissionStatus(pendingStatus);
        
        // 第二阶段：申请后环节
        progress.setStepInterviewInvitationStatus(pendingStatus);
        progress.setStepInterviewTrainingStatus(pendingStatus);
        progress.setStepAttendInterviewStatus(pendingStatus);
        progress.setStepAdmissionNoticeStatus(pendingStatus);
        
        // 第三阶段：录取后流程
        progress.setStepAcceptOfferDepositStatus(pendingStatus);
        progress.setStepVisaPreparationStatus(pendingStatus);
        progress.setStepSchoolVisaUploadStatus(pendingStatus);
        
        // 第四阶段：最终确认阶段
        progress.setStepConditionalMaterialsStatus(pendingStatus);
        progress.setStepVisaFormalOfferStatus(pendingStatus);
        progress.setStepAccommodationRegistrationStatus(pendingStatus);

        // 设置业务字段默认值
        progress.setIsReceiveAdmission(CommonEnums.WhetherEnum.N.name());
        progress.setIsPayDeposit(CommonEnums.WhetherEnum.N.name());
        progress.setIsPayVisaFee(CommonEnums.WhetherEnum.N.name());
        progress.setIsReceiveFormalOffer(CommonEnums.WhetherEnum.N.name());
        progress.setAccommodationApplication(CommonEnums.WhetherEnum.N.name());
        progress.setIsConfirmEnrollment(CommonEnums.WhetherEnum.N.name());

        progress.setDelFlag(UserStatus.OK.getCode());
        progress.setCreateTime(DateUtils.getNowDate());

        // 保存进度记录
        save(progress);

        return progress;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProgress(Long progressId, UpdateProgressDto updateData, Long operatorId) {
        // 使用JposAssert进行空值验证
        JposAssert.notNull(progressId, "进度ID不能为空");
        JposAssert.notNull(updateData, "更新数据不能为空");
        JposAssert.notNull(operatorId, "操作人ID不能为空");

        TStMaterialPlanProgress progress = getById(progressId);
        JposAssert.notNull(progress, "进度记录不存在");

        // 记录更新前的状态用于日志
        String beforeCurrentStep = progress.getCurrentStep();
        MaterialPlanProgressEnum.OperationType operationType = MaterialPlanProgressEnum.OperationType.UPDATE;
        String operationDesc = "更新进度信息";

        // 根据更新类型进行相应的更新
        String updateType = updateData.getUpdateType();
        JposAssert.notNull(updateType, "更新类型不能为空");

        switch (updateType) {
            case "stepCode":
                // 更新步骤状态 - 新的14步骤流程
                String stepCodeStr = updateData.getStepCode();
                String stepStatusStr = updateData.getStepStatus();

                JposAssert.notNull(stepCodeStr, "步骤编码不能为空");
                JposAssert.notNull(stepStatusStr, "步骤状态不能为空");

                // 验证步骤跳过限制
                if ("SKIPPED".equals(stepStatusStr)) {
                    validateStepSkipPermission(stepCodeStr);
                }

                updateStepStatusByCode(progress, stepCodeStr, stepStatusStr, updateData);

                // 如果步骤完成或跳过，自动进入下一步骤
                if ("COMPLETED".equals(stepStatusStr) || "SKIPPED".equals(stepStatusStr)) {
                    String nextStep = getNextStepCode(stepCodeStr);
                    if (nextStep != null) {
                        progress.setCurrentStep(nextStep);
                        progress.setCurrentStepName(getStepName(nextStep));
                    } else {
                        // 所有步骤完成，整个流程已结束
                        progress.setProgressStatus(MaterialPlanProgressEnum.ProgressStatus.COMPLETED.getCode());
                        operationDesc = "完成最后步骤，整个流程已结束";
                    }
                }

                // 特别处理：如果是最后一个步骤完成，直接结束整个流程
                if ("ACCOMMODATION_REGISTRATION".equals(stepCodeStr) && "COMPLETED".equals(stepStatusStr)) {
                    progress.setProgressStatus(MaterialPlanProgressEnum.ProgressStatus.COMPLETED.getCode());
                    operationDesc = "住宿申请&注册步骤完成，整个流程已结束";
                }

                // 处理操作数据（文件数据）
                if (StringUtils.hasText(updateData.getOperationData())) {
                    updateStepDataByCode(progress, stepCodeStr, updateData.getOperationData());
                }
                operationDesc = "更新步骤状态: " + getStepName(stepCodeStr);
                break;

            case "progressStatus":
                // 直接更新整体进度状态（用于结束流程等操作）
                String newProgressStatus = updateData.getProgressStatus();
                JposAssert.notNull(newProgressStatus, "进度状态不能为空");

                progress.setProgressStatus(newProgressStatus);
                operationDesc = "更新整体进度状态: " + newProgressStatus;

                // 如果设置为COMPLETED（结束流程），只更新流程状态，步骤保持当前位置
                if (MaterialPlanProgressEnum.ProgressStatus.COMPLETED.getCode().equals(newProgressStatus)) {
                    operationDesc = "手动结束流程，当前步骤位置保持不变";
                    // 注意：不调用completeAllRemainingSteps，保持步骤状态不变
                }
                break;

            default:
                throw new IllegalArgumentException("未知的更新类型: " + updateType);
        }

        progress.setUpdateTime(DateUtils.getNowDate());
        boolean result = updateById(progress);

        // 记录操作日志
        if (result) {
            // 获取步骤枚举，如果新步骤不存在则使用默认值
            MaterialPlanProgressEnum.StepCode currentStepEnum = MaterialPlanProgressEnum.StepCode.get(progress.getCurrentStep());
            MaterialPlanProgressEnum.StepCode beforeStepEnum = MaterialPlanProgressEnum.StepCode.get(beforeCurrentStep);
            
            // 如果枚举为null，使用第一个步骤作为默认值
            if (currentStepEnum == null) {
                currentStepEnum = MaterialPlanProgressEnum.StepCode.WRITING_PROCESS;
            }
            if (beforeStepEnum == null) {
                beforeStepEnum = MaterialPlanProgressEnum.StepCode.WRITING_PROCESS;
            }

            MaterialPlanProgressLogDto logDto = new MaterialPlanProgressLogDto(
                    progress.getPlanId(), progressId, progress.getStId(), progress.getContractId(),
                    progress.getOrderId(), progress.getApplySchool(), progress.getApplyMajorZh(),
                    currentStepEnum,
                    operationType, operationDesc, operatorId
            );

            logDto.withStepChange(beforeStepEnum, currentStepEnum);

            logService.addLog(logDto);
        }

        return result;
    }

    /**
     * 根据步骤编码更新对应的步骤状态 - 新的14步骤流程
     */
    private void updateStepStatusByCode(TStMaterialPlanProgress progress, String stepCode,
                                        String stepStatus, UpdateProgressDto updateData) {
        Date now = DateUtils.getNowDate();
        boolean isCompleted = "COMPLETED".equals(stepStatus);
        String remark = updateData.getStepRemark();
        Date selectDateTime = updateData.getStepSelectTime();

        switch (stepCode) {
            // 第一阶段：申请准备阶段
            case "WRITING_PROCESS":
                progress.setStepWritingProcessStatus(stepStatus);
                if (isCompleted) progress.setStepWritingProcessOperationTime(now);
                if (selectDateTime != null) progress.setStepWritingProcessSelectTime(selectDateTime);
                progress.setStepWritingProcessRemark(remark);
                break;
                
            case "WRITING_FINALIZATION":
                progress.setStepWritingFinalizationStatus(stepStatus);
                if (isCompleted) progress.setStepWritingFinalizationOperationTime(now);
                if (selectDateTime != null) progress.setStepWritingFinalizationSelectTime(selectDateTime);
                progress.setStepWritingFinalizationRemark(remark);
                break;
                
            case "APPLICATION_FILLING":
                progress.setStepApplicationFillingStatus(stepStatus);
                if (isCompleted) progress.setStepApplicationFillingOperationTime(now);
                if (selectDateTime != null) progress.setStepApplicationFillingSelectTime(selectDateTime);
                progress.setStepApplicationFillingRemark(remark);

                // 学校申请填写步骤：更新申请相关信息
                if (isCompleted) {
                    updateApplicationInfo(progress, updateData);
                }
                break;
                
            case "APPLICATION_SUBMISSION":
                progress.setStepApplicationSubmissionStatus(stepStatus);
                if (isCompleted) progress.setStepApplicationSubmissionOperationTime(now);
                if (selectDateTime != null) progress.setStepApplicationSubmissionSelectTime(selectDateTime);
                progress.setStepApplicationSubmissionRemark(remark);
                
                // 申请递交确认步骤：记录递交时间
                if (isCompleted && updateData.getApplicationSubmissionTime() != null) {
                    progress.setApplicationSubmissionTime(updateData.getApplicationSubmissionTime());
                }
                break;
                
            // 第二阶段：申请后环节
            case "INTERVIEW_INVITATION":
                progress.setStepInterviewInvitationStatus(stepStatus);
                if (isCompleted) progress.setStepInterviewInvitationOperationTime(now);
                if (selectDateTime != null) {
                    progress.setStepInterviewInvitationSelectTime(selectDateTime);
                    progress.setInterviewDateTime(selectDateTime);
                }
                progress.setStepInterviewInvitationRemark(remark);
                
                // 面试邀请通知步骤：更新面试链接和时间
                updateInterviewInfo(progress, updateData);
                break;
                
            case "INTERVIEW_TRAINING":
                progress.setStepInterviewTrainingStatus(stepStatus);
                if (isCompleted) progress.setStepInterviewTrainingOperationTime(now);
                if (selectDateTime != null) progress.setStepInterviewTrainingSelectTime(selectDateTime);
                progress.setStepInterviewTrainingRemark(remark);
                break;
                
            case "ATTEND_INTERVIEW":
                progress.setStepAttendInterviewStatus(stepStatus);
                if (isCompleted) progress.setStepAttendInterviewOperationTime(now);
                if (selectDateTime != null) progress.setStepAttendInterviewSelectTime(selectDateTime);
                progress.setStepAttendInterviewRemark(remark);
                break;
                
            case "ADMISSION_NOTICE":
                progress.setStepAdmissionNoticeStatus(stepStatus);
                if (isCompleted) progress.setStepAdmissionNoticeOperationTime(now);
                if (selectDateTime != null) progress.setStepAdmissionNoticeSelectTime(selectDateTime);
                progress.setStepAdmissionNoticeRemark(remark);
                
                // 录取通知步骤：处理录取相关信息
                if (isCompleted) {
                    updateAdmissionInfo(progress, updateData, selectDateTime, now);
                }
                break;
                
            // 第三阶段：录取后流程
            case "ACCEPT_OFFER_DEPOSIT":
                progress.setStepAcceptOfferDepositStatus(stepStatus);
                if (isCompleted) progress.setStepAcceptOfferDepositOperationTime(now);
                if (selectDateTime != null) progress.setStepAcceptOfferDepositSelectTime(selectDateTime);
                progress.setStepAcceptOfferDepositRemark(remark);
                
                // 接受录取&留位费步骤：处理留位费信息
                if (isCompleted) {
                    updateDepositInfo(progress, updateData);
                }
                break;
                
            case "VISA_PREPARATION":
                progress.setStepVisaPreparationStatus(stepStatus);
                if (isCompleted) progress.setStepVisaPreparationOperationTime(now);
                if (selectDateTime != null) progress.setStepVisaPreparationSelectTime(selectDateTime);
                progress.setStepVisaPreparationRemark(remark);
                break;
                
            case "SCHOOL_VISA_UPLOAD":
                progress.setStepSchoolVisaUploadStatus(stepStatus);
                if (isCompleted) progress.setStepSchoolVisaUploadOperationTime(now);
                if (selectDateTime != null) progress.setStepSchoolVisaUploadSelectTime(selectDateTime);
                progress.setStepSchoolVisaUploadRemark(remark);

                // 学校签证系统上传步骤：处理签证行政费信息
                if (isCompleted) {
                    updateVisaFeeInfo(progress, updateData);
                }
                break;
                
            // 第四阶段：最终确认阶段
            case "CONDITIONAL_MATERIALS":
                progress.setStepConditionalMaterialsStatus(stepStatus);
                if (isCompleted) progress.setStepConditionalMaterialsOperationTime(now);
                if (selectDateTime != null) progress.setStepConditionalMaterialsSelectTime(selectDateTime);
                progress.setStepConditionalMaterialsRemark(remark);
                break;
                
            case "VISA_FORMAL_OFFER":
                progress.setStepVisaFormalOfferStatus(stepStatus);
                if (isCompleted) progress.setStepVisaFormalOfferOperationTime(now);
                if (selectDateTime != null) progress.setStepVisaFormalOfferSelectTime(selectDateTime);
                progress.setStepVisaFormalOfferRemark(remark);

                // 签证审批&正式录取步骤：处理签证和正式录取信息
                if (isCompleted) {
                    updateVisaAndFormalOfferInfo(progress, updateData, selectDateTime, now);
                        }
                break;
                
            case "ACCOMMODATION_REGISTRATION":
                progress.setStepAccommodationRegistrationStatus(stepStatus);
                if (isCompleted) progress.setStepAccommodationRegistrationOperationTime(now);
                if (selectDateTime != null) progress.setStepAccommodationRegistrationSelectTime(selectDateTime);
                progress.setStepAccommodationRegistrationRemark(remark);
                
                // 住宿申请&注册步骤：处理住宿和入学确认信息
                if (isCompleted) {
                    updateAccommodationAndEnrollmentInfo(progress, updateData, selectDateTime, now);
                }
                break;
                
            default:
                throw new IllegalArgumentException("未知的步骤编码: " + stepCode);
        }
    }

    /**
     * 检查该学生是否已有其他确认就读记录
     * @param stId 学生ID
     * @param currentProgressId 当前进度记录ID（排除自己）
     * @return true表示已有其他确认就读记录
     */
    private boolean checkOtherConfirmedEnrollment(Long stId, Long currentProgressId) {
        return lambdaQuery()
                .eq(TStMaterialPlanProgress::getStId, stId)
                .eq(TStMaterialPlanProgress::getIsConfirmEnrollment, CommonEnums.WhetherEnum.Y.name())
                .eq(TStMaterialPlanProgress::getDelFlag, UserStatus.OK.getCode())
                .ne(currentProgressId != null, TStMaterialPlanProgress::getId, currentProgressId)
                .exists();
    }

    /**
     * 根据步骤编码更新对应的步骤数据 - 新的14步骤流程
     */
    private void updateStepDataByCode(TStMaterialPlanProgress progress, String stepCode, String operationData) {
        switch (stepCode) {
            case "WRITING_PROCESS":
                progress.setStepWritingProcessData(operationData);
                break;
            case "WRITING_FINALIZATION":
                progress.setStepWritingFinalizationData(operationData);
                break;
            case "APPLICATION_FILLING":
                progress.setStepApplicationFillingData(operationData);
                break;
            case "APPLICATION_SUBMISSION":
                progress.setStepApplicationSubmissionData(operationData);
                break;
            case "INTERVIEW_INVITATION":
                progress.setStepInterviewInvitationData(operationData);
                break;
            case "INTERVIEW_TRAINING":
                progress.setStepInterviewTrainingData(operationData);
                break;
            case "ATTEND_INTERVIEW":
                progress.setStepAttendInterviewData(operationData);
                break;
            case "ADMISSION_NOTICE":
                progress.setStepAdmissionNoticeData(operationData);
                break;
            case "ACCEPT_OFFER_DEPOSIT":
                progress.setStepAcceptOfferDepositData(operationData);
                break;
            case "VISA_PREPARATION":
                progress.setStepVisaPreparationData(operationData);
                break;
            case "SCHOOL_VISA_UPLOAD":
                progress.setStepSchoolVisaUploadData(operationData);
                break;
            case "CONDITIONAL_MATERIALS":
                progress.setStepConditionalMaterialsData(operationData);
                break;
            case "VISA_FORMAL_OFFER":
                progress.setStepVisaFormalOfferData(operationData);
                break;
            case "ACCOMMODATION_REGISTRATION":
                progress.setStepAccommodationRegistrationData(operationData);
                break;
            default:
                // 忽略未知步骤
                break;
        }
    }

    /**
     * 验证步骤跳过权限 - 新的14步骤流程
     * 某些关键步骤不允许跳过，必须完成相应的选择
     *
     * @param stepCode 步骤编码
     * @throws IllegalArgumentException 如果步骤不允许跳过
     */
    private void validateStepSkipPermission(String stepCode) {
        switch (stepCode) {
            case "WRITING_FINALIZATION":
                throw new IllegalArgumentException("文书定稿步骤不能跳过，必须确认文书完成状态");
            case "APPLICATION_FILLING":
                throw new IllegalArgumentException("学校申请填写步骤不能跳过，必须完成申请填写");
            case "APPLICATION_SUBMISSION":
                throw new IllegalArgumentException("申请递交确认步骤不能跳过，必须确认递交状态");
            case "ADMISSION_NOTICE":
                throw new IllegalArgumentException("录取通知步骤不能跳过，必须选择是否收到录取通知");
            case "VISA_FORMAL_OFFER":
                throw new IllegalArgumentException("签证审批&正式录取步骤不能跳过，必须选择最终录取状态");
            case "ACCOMMODATION_REGISTRATION":
                throw new IllegalArgumentException("住宿申请&注册步骤不能跳过，注册为所有学生的必办流程");
            default:
                // 其他步骤允许跳过
                break;
        }
    }

    /**
     * 获取下一个步骤编码
     */
    private String getNextStepCode(String currentStep) {
        switch (currentStep) {
            case "WRITING_PROCESS": return "WRITING_FINALIZATION";
            case "WRITING_FINALIZATION": return "APPLICATION_FILLING";
            case "APPLICATION_FILLING": return "APPLICATION_SUBMISSION";
            case "APPLICATION_SUBMISSION": return "INTERVIEW_INVITATION";
            case "INTERVIEW_INVITATION": return "INTERVIEW_TRAINING";
            case "INTERVIEW_TRAINING": return "ATTEND_INTERVIEW";
            case "ATTEND_INTERVIEW": return "ADMISSION_NOTICE";
            case "ADMISSION_NOTICE": return "ACCEPT_OFFER_DEPOSIT";
            case "ACCEPT_OFFER_DEPOSIT": return "VISA_PREPARATION";
            case "VISA_PREPARATION": return "SCHOOL_VISA_UPLOAD";
            case "SCHOOL_VISA_UPLOAD": return "CONDITIONAL_MATERIALS";
            case "CONDITIONAL_MATERIALS": return "VISA_FORMAL_OFFER";
            case "VISA_FORMAL_OFFER": return "ACCOMMODATION_REGISTRATION";
            case "ACCOMMODATION_REGISTRATION": return null; // 最后一个步骤
            default: return null;
        }
    }

    /**
     * 获取步骤名称
     */
    private String getStepName(String stepCode) {
        switch (stepCode) {
            case "WRITING_PROCESS": return "文书环节";
            case "WRITING_FINALIZATION": return "文书定稿";
            case "APPLICATION_FILLING": return "学校申请填写";
            case "APPLICATION_SUBMISSION": return "申请递交确认";
            case "INTERVIEW_INVITATION": return "面试邀请通知";
            case "INTERVIEW_TRAINING": return "面试培训安排";
            case "ATTEND_INTERVIEW": return "参加面试";
            case "ADMISSION_NOTICE": return "录取通知";
            case "ACCEPT_OFFER_DEPOSIT": return "接受录取&留位费";
            case "VISA_PREPARATION": return "签证申请准备";
            case "SCHOOL_VISA_UPLOAD": return "学校签证系统上传";
            case "CONDITIONAL_MATERIALS": return "条件录取材料递交";
            case "VISA_FORMAL_OFFER": return "签证审批&正式录取";
            case "ACCOMMODATION_REGISTRATION": return "住宿申请&注册";
            default: return "未知步骤";
        }
    }

    /**
     * 更新申请信息
     */
    private void updateApplicationInfo(TStMaterialPlanProgress progress, UpdateProgressDto updateData) {
        if (StringUtils.hasText(updateData.getApplicationNumber())) {
            progress.setApplicationNumber(updateData.getApplicationNumber());
        }
        if (StringUtils.hasText(updateData.getApplicationEmail())) {
            progress.setApplicationEmail(updateData.getApplicationEmail());
        }
        if (StringUtils.hasText(updateData.getApplicationPassword())) {
            progress.setApplicationPassword(updateData.getApplicationPassword());
        }
    }

    /**
     * 更新面试信息
     */
    private void updateInterviewInfo(TStMaterialPlanProgress progress, UpdateProgressDto updateData) {
        if (StringUtils.hasText(updateData.getInterviewLink())) {
            progress.setInterviewLink(updateData.getInterviewLink());
        }
        if (updateData.getInterviewDateTime() != null) {
            progress.setInterviewDateTime(updateData.getInterviewDateTime());
        }
    }

    /**
     * 更新录取通知信息
     */
    private void updateAdmissionInfo(TStMaterialPlanProgress progress, UpdateProgressDto updateData, 
                                   Date selectDateTime, Date now) {
        // 更新是否收到录取通知状态
        if (updateData.getReceiveAdmission() != null) {
            progress.setIsReceiveAdmission(updateData.getReceiveAdmission() ? 
                CommonEnums.WhetherEnum.Y.name() : CommonEnums.WhetherEnum.N.name());
        } else if (StringUtils.hasText(updateData.getIsReceiveAdmission())) {
            progress.setIsReceiveAdmission(updateData.getIsReceiveAdmission());
        }

        // 如果收到录取通知，更新相关信息
        if (CommonEnums.WhetherEnum.Y.name().equals(progress.getIsReceiveAdmission())) {
            progress.setReceiveAdmissionTime(selectDateTime != null ? selectDateTime : now);
            
            if (StringUtils.hasText(updateData.getAdmissionType())) {
                progress.setAdmissionType(updateData.getAdmissionType());
            }
            
            if (StringUtils.hasText(updateData.getAdmissionConditions())) {
                progress.setAdmissionConditions(updateData.getAdmissionConditions());
            }
        } else {
            // 未收到录取通知，清空相关字段
            progress.setReceiveAdmissionTime(null);
            progress.setAdmissionType(null);
            progress.setAdmissionConditions(null);
        }
    }

    /**
     * 更新留位费信息
     */
    private void updateDepositInfo(TStMaterialPlanProgress progress, UpdateProgressDto updateData) {
        if (updateData.getDepositDeadline() != null) {
            progress.setDepositDeadline(updateData.getDepositDeadline());
        }
        
        if (StringUtils.hasText(updateData.getDepositAmount())) {
            try {
                progress.setDepositAmount(new java.math.BigDecimal(updateData.getDepositAmount()));
            } catch (NumberFormatException e) {
                // 忽略格式错误
            }
        }
        
        if (StringUtils.hasText(updateData.getIsPayDeposit())) {
            progress.setIsPayDeposit(updateData.getIsPayDeposit());
            if (CommonEnums.WhetherEnum.Y.name().equals(updateData.getIsPayDeposit()) 
                && updateData.getPayDepositTime() != null) {
                progress.setPayDepositTime(updateData.getPayDepositTime());
            }
        }
    }

    /**
     * 更新签证行政费信息
     */
    private void updateVisaFeeInfo(TStMaterialPlanProgress progress, UpdateProgressDto updateData) {
        if (StringUtils.hasText(updateData.getVisaAdminFee())) {
            try {
                progress.setVisaAdminFee(new java.math.BigDecimal(updateData.getVisaAdminFee()));
            } catch (NumberFormatException e) {
                // 忽略格式错误
            }
        }
        
        if (StringUtils.hasText(updateData.getIsPayVisaFee())) {
            progress.setIsPayVisaFee(updateData.getIsPayVisaFee());
            if (CommonEnums.WhetherEnum.Y.name().equals(updateData.getIsPayVisaFee()) 
                && updateData.getPayVisaFeeTime() != null) {
                progress.setPayVisaFeeTime(updateData.getPayVisaFeeTime());
            }
        }
    }

    /**
     * 更新签证和正式录取信息
     */
    private void updateVisaAndFormalOfferInfo(TStMaterialPlanProgress progress, UpdateProgressDto updateData, 
                                             Date selectDateTime, Date now) {
        // 更新签证审批状态
        if (StringUtils.hasText(updateData.getVisaApprovalStatus())) {
            progress.setVisaApprovalStatus(updateData.getVisaApprovalStatus());
            if ("APPROVED".equals(updateData.getVisaApprovalStatus()) && updateData.getVisaApprovalTime() != null) {
                progress.setVisaApprovalTime(updateData.getVisaApprovalTime());
            }
        }
        
        // 更新正式录取信息
        if (StringUtils.hasText(updateData.getIsReceiveFormalOffer())) {
            progress.setIsReceiveFormalOffer(updateData.getIsReceiveFormalOffer());
            if (CommonEnums.WhetherEnum.Y.name().equals(updateData.getIsReceiveFormalOffer()) 
                && updateData.getReceiveFormalOfferTime() != null) {
                progress.setReceiveFormalOfferTime(updateData.getReceiveFormalOfferTime());
            }
        }
    }

    /**
     * 更新住宿和入学确认信息
     */
    private void updateAccommodationAndEnrollmentInfo(TStMaterialPlanProgress progress, UpdateProgressDto updateData, 
                                                     Date selectDateTime, Date now) {
        // 更新住宿申请信息
        if (StringUtils.hasText(updateData.getAccommodationApplication())) {
            progress.setAccommodationApplication(updateData.getAccommodationApplication());
            if (CommonEnums.WhetherEnum.Y.name().equals(updateData.getAccommodationApplication()) 
                && updateData.getAccommodationApplicationTime() != null) {
                progress.setAccommodationApplicationTime(updateData.getAccommodationApplicationTime());
            }
        }
        
        // 更新入学确认信息
        if (updateData.getConfirmEnrollment() != null) {
            String enrollmentStatus = updateData.getConfirmEnrollment() ? 
                CommonEnums.WhetherEnum.Y.name() : CommonEnums.WhetherEnum.N.name();
            
            if (updateData.getConfirmEnrollment()) {
                // 检查该学生是否已有其他确认就读记录
                boolean hasOtherConfirmedEnrollment = checkOtherConfirmedEnrollment(progress.getStId(), progress.getId());
                if (hasOtherConfirmedEnrollment) {
                    throw new IllegalStateException("该学生已有其他确认就读记录，一个学生只能确认一个入学");
                }
                
                progress.setIsConfirmEnrollment(enrollmentStatus);
                progress.setConfirmEnrollmentTime(selectDateTime != null ? selectDateTime : now);
            } else {
                progress.setIsConfirmEnrollment(enrollmentStatus);
                progress.setConfirmEnrollmentTime(null);
            }
        } else if (StringUtils.hasText(updateData.getIsConfirmEnrollment())) {
            progress.setIsConfirmEnrollment(updateData.getIsConfirmEnrollment());
            if (CommonEnums.WhetherEnum.Y.name().equals(updateData.getIsConfirmEnrollment()) 
                && updateData.getConfirmEnrollmentTime() != null) {
                progress.setConfirmEnrollmentTime(updateData.getConfirmEnrollmentTime());
            }
        }
    }
}