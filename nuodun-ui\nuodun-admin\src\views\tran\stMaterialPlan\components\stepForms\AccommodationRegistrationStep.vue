<template>
  <div class="accommodation-registration-step">
    <!-- 查看模式 -->
    <div v-if="readonly" class="view-mode">
      <div class="step-info">
        <h4>住宿申请&注册详情</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>住宿申请状态：</label>
            <el-tag :type="stepData.accommodationApplied ? 'success' : 'info'">
              {{ stepData.accommodationApplied ? '已申请' : '未申请' }}
            </el-tag>
          </div>
          <div v-if="stepData.accommodationApplied" class="info-item">
            <label>住宿申请时间：</label>
            <span>{{ parseTime(stepData.accommodationTime) || '暂无' }}</span>
          </div>
          <div class="info-item">
            <label>注册状态：</label>
            <el-tag :type="stepData.registrationCompleted ? 'success' : 'warning'">
              {{ stepData.registrationCompleted ? '已注册' : '待注册' }}
            </el-tag>
          </div>
          <div v-if="stepData.registrationCompleted" class="info-item">
            <label>注册时间：</label>
            <span>{{ parseTime(stepData.registrationTime) || '暂无' }}</span>
          </div>
          <div class="info-item">
            <label>最终入学确认：</label>
            <el-tag :type="stepData.enrollmentConfirmed ? 'success' : 'danger'">
              {{ stepData.enrollmentConfirmed ? '已确认入学' : '未确认入学' }}
            </el-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑模式 -->
    <div v-else class="edit-mode">
      <el-form :model="stepData" :rules="rules" ref="formRef" label-width="140px">
        <el-form-item label="住宿申请状态" prop="accommodationApplied">
          <el-radio-group v-model="stepData.accommodationApplied">
            <el-radio :label="true">已申请住宿</el-radio>
            <el-radio :label="false">未申请住宿</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item 
          v-if="stepData.accommodationApplied" 
          label="住宿申请时间" 
          prop="accommodationTime"
        >
          <el-date-picker
            v-model="stepData.accommodationTime"
            type="datetime"
            placeholder="选择住宿申请时间"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>

        <el-form-item label="注册状态" prop="registrationCompleted">
          <el-radio-group v-model="stepData.registrationCompleted">
            <el-radio :label="true">已完成注册</el-radio>
            <el-radio :label="false">待完成注册</el-radio>
          </el-radio-group>
          <div class="form-tip">
            <el-text type="info" size="small">
              注册为所有学生的必办流程
            </el-text>
          </div>
        </el-form-item>

        <el-form-item 
          v-if="stepData.registrationCompleted" 
          label="注册时间" 
          prop="registrationTime"
        >
          <el-date-picker
            v-model="stepData.registrationTime"
            type="datetime"
            placeholder="选择注册时间"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>

        <el-form-item label="最终入学确认" prop="enrollmentConfirmed">
          <el-radio-group v-model="stepData.enrollmentConfirmed">
            <el-radio :label="true">已确认入学</el-radio>
            <el-radio :label="false">未确认入学</el-radio>
          </el-radio-group>
          <div class="form-tip">
            <el-text type="warning" size="small">
              注意：一个学生只能确认一个入学，确认后无法修改
            </el-text>
          </div>
        </el-form-item>

        <el-form-item label="备注说明" prop="remark">
          <el-input
            v-model="stepData.remark"
            type="textarea"
            :rows="3"
            placeholder="请描述住宿申请和注册的相关情况"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { parseTime } from '@/utils/ruoyi'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const formRef = ref(null)

// 步骤数据
const stepData = reactive({
  accommodationApplied: false,
  accommodationTime: '',
  registrationCompleted: false,
  registrationTime: '',
  enrollmentConfirmed: null,
  remark: ''
})

// 验证规则
const rules = {
  accommodationApplied: [
    { required: true, message: '请选择住宿申请状态', trigger: 'change' }
  ],
  accommodationTime: [
    { required: true, message: '请选择住宿申请时间', trigger: 'change', validator: (rule, value, callback) => {
      if (stepData.accommodationApplied && !value) {
        callback(new Error('请选择住宿申请时间'))
      } else {
        callback()
      }
    }}
  ],
  registrationCompleted: [
    { required: true, message: '请选择注册状态', trigger: 'change' }
  ],
  registrationTime: [
    { required: true, message: '请选择注册时间', trigger: 'change', validator: (rule, value, callback) => {
      if (stepData.registrationCompleted && !value) {
        callback(new Error('请选择注册时间'))
      } else {
        callback()
      }
    }}
  ],
  enrollmentConfirmed: [
    { required: true, message: '请选择入学确认状态', trigger: 'change' }
  ]
}

// 初始化数据
const initData = () => {
  Object.assign(stepData, {
    accommodationApplied: false,
    accommodationTime: '',
    registrationCompleted: false,
    registrationTime: '',
    enrollmentConfirmed: null,
    remark: '',
    ...props.modelValue
  })
}

// 监听数据变化
watch(() => stepData, (newData) => {
  emit('update:modelValue', { ...newData })
  emit('change', { ...newData })
}, { deep: true })

// 监听props变化
watch(() => props.modelValue, () => {
  initData()
}, { immediate: true })

// 验证表单
const validate = async () => {
  if (!formRef.value) return true
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    return false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(stepData, {
    accommodationApplied: false,
    accommodationTime: '',
    registrationCompleted: false,
    registrationTime: '',
    enrollmentConfirmed: null,
    remark: ''
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

defineExpose({
  validate,
  resetForm
})
</script>

<style scoped lang="scss">
.accommodation-registration-step {
  .view-mode {
    .step-info {
      h4 {
        margin-bottom: 16px;
        color: #303133;
        font-weight: 600;
      }

      .info-grid {
        display: grid;
        gap: 12px;

        .info-item {
          display: flex;
          align-items: flex-start;

          label {
            min-width: 120px;
            font-weight: 500;
            color: #606266;
          }

          span {
            color: #303133;
            word-break: break-all;
          }
        }
      }
    }
  }

  .edit-mode {
    .el-form {
      max-width: 600px;
    }

    .form-tip {
      margin-top: 8px;
      padding: 8px 12px;
      background: #f0f9ff;
      border: 1px solid #b3d8ff;
      border-radius: 4px;

      &:has(.el-text--warning) {
        background: #fdf6ec;
        border-color: #faecd8;
      }
    }
  }
}
</style> 