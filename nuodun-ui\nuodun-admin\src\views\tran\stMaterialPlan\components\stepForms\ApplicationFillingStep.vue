<template>
  <div class="application-filling-step">
    <!-- 查看模式 -->
    <div v-if="readonly" class="view-mode">
      <div class="step-info">
        <h4>学校申请填写详情</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>申请编号：</label>
            <span>{{ stepData.applicationNumber || '暂未填写' }}</span>
          </div>
          <div class="info-item">
            <label>申请邮箱：</label>
            <span>{{ stepData.applicationEmail || '暂未填写' }}</span>
          </div>
          <div class="info-item">
            <label>申请密码：</label>
            <span>{{ stepData.applicationPassword ? '已设置' : '暂未设置' }}</span>
          </div>
          <div class="info-item">
            <label>填写进度：</label>
            <el-progress 
              :percentage="stepData.fillingProgress || 0" 
              :color="getProgressColor(stepData.fillingProgress)"
              :text-inside="true"
              :stroke-width="18"
            />
          </div>
          <div v-if="stepData.applicationDescription" class="info-item">
            <label>申请信息说明：</label>
            <span>{{ stepData.applicationDescription }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑模式 -->
    <div v-else class="edit-mode">
      <el-form :model="stepData" :rules="rules" ref="formRef" label-width="140px">
        <el-form-item label="申请编号" prop="applicationNumber">
          <el-input v-model="stepData.applicationNumber" placeholder="请输入学校申请系统的申请编号" />
        </el-form-item>

        <el-form-item label="申请邮箱" prop="applicationEmail">
          <el-input v-model="stepData.applicationEmail" placeholder="请输入申请邮箱" />
        </el-form-item>

        <el-form-item label="申请密码" prop="applicationPassword">
          <el-input 
            v-model="stepData.applicationPassword" 
            type="password" 
            placeholder="请输入申请密码"
            show-password
          />
        </el-form-item>

        <el-form-item label="填写进度" prop="fillingProgress">
          <el-slider 
            v-model="stepData.fillingProgress" 
            :marks="progressMarks"
            :max="100"
            show-tooltip
          />
        </el-form-item>

        <el-form-item label="申请信息说明" prop="applicationDescription">
          <el-input
            v-model="stepData.applicationDescription"
            type="textarea"
            :rows="3"
            placeholder="请描述申请填写的具体情况和注意事项"
          />
        </el-form-item>

        <el-form-item label="备注说明" prop="remark">
          <el-input
            v-model="stepData.remark"
            type="textarea"
            :rows="2"
            placeholder="其他备注信息"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { parseTime } from '@/utils/ruoyi'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const formRef = ref(null)

// 步骤数据
const stepData = reactive({
  applicationNumber: '',
  applicationEmail: '',
  applicationPassword: '',
  fillingProgress: 0,
  applicationDescription: '',
  remark: ''
})

// 进度标记
const progressMarks = {
  0: '未开始',
  25: '25%',
  50: '50%',
  75: '75%',
  100: '完成'
}

// 验证规则
const rules = {
  applicationNumber: [
    { required: true, message: '请输入申请编号', trigger: 'blur' }
  ],
  applicationEmail: [
    { required: true, message: '请输入申请邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  applicationPassword: [
    { required: true, message: '请输入申请密码', trigger: 'blur' }
  ],
  fillingProgress: [
    { required: true, message: '请设置填写进度', trigger: 'change' }
  ]
}

// 获取进度颜色
const getProgressColor = (percentage) => {
  if (percentage >= 100) return '#67c23a'
  if (percentage >= 75) return '#409eff'
  if (percentage >= 50) return '#e6a23c'
  return '#f56c6c'
}

// 初始化数据
const initData = () => {
  Object.assign(stepData, {
    applicationNumber: '',
    applicationEmail: '',
    applicationPassword: '',
    fillingProgress: 0,
    applicationDescription: '',
    remark: '',
    ...props.modelValue
  })
}

// 监听数据变化
watch(() => stepData, (newData) => {
  emit('update:modelValue', { ...newData })
  emit('change', { ...newData })
}, { deep: true })

// 监听props变化
watch(() => props.modelValue, () => {
  initData()
}, { immediate: true })

// 验证表单
const validate = async () => {
  if (!formRef.value) return true
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    return false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(stepData, {
    applicationNumber: '',
    applicationEmail: '',
    applicationPassword: '',
    fillingProgress: 0,
    applicationDescription: '',
    remark: ''
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

defineExpose({
  validate,
  resetForm
})
</script>

<style scoped lang="scss">
.application-filling-step {
  .view-mode {
    .step-info {
      h4 {
        margin-bottom: 16px;
        color: #303133;
        font-weight: 600;
      }

      .info-grid {
        display: grid;
        gap: 12px;

        .info-item {
          display: flex;
          align-items: flex-start;

          label {
            min-width: 120px;
            font-weight: 500;
            color: #606266;
          }

          span {
            color: #303133;
            word-break: break-all;
          }
        }
      }
    }
  }

  .edit-mode {
    .el-form {
      max-width: 600px;
    }
  }
}
</style> 