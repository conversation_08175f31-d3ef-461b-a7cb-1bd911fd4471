<template>
  <div class="unified-step-form">
    <!-- 步骤特定组件 -->
    <div class="step-specific-form">
      <!-- 文书环节 -->
      <WritingProcessStep 
        v-if="stepCode === 'WRITING_PROCESS'"
        v-model="stepSpecificData"
        :readonly="readonly"
        @change="handleStepDataChange"
      />
      
      <!-- 文书定稿 -->
      <WritingFinalizationStep 
        v-else-if="stepCode === 'WRITING_FINALIZATION'"
        v-model="stepSpecificData"
        :readonly="readonly"
        @change="handleStepDataChange"
      />
      
      <!-- 学校申请填写 -->
      <ApplicationFillingStep 
        v-else-if="stepCode === 'APPLICATION_FILLING'"
        v-model="stepSpecificData"
        :readonly="readonly"
        @change="handleStepDataChange"
      />
      
      <!-- 申请递交确认 -->
      <ApplicationSubmissionStep 
        v-else-if="stepCode === 'APPLICATION_SUBMISSION'"
        v-model="stepSpecificData"
        :readonly="readonly"
        @change="handleStepDataChange"
      />
      
      <!-- 录取通知 -->
      <AdmissionNoticeStep 
        v-else-if="stepCode === 'ADMISSION_NOTICE'"
        v-model="stepSpecificData"
        :readonly="readonly"
        @change="handleStepDataChange"
      />
      
      <!-- 住宿申请&注册 -->
      <AccommodationRegistrationStep 
        v-else-if="stepCode === 'ACCOMMODATION_REGISTRATION'"
        v-model="stepSpecificData"
        :readonly="readonly"
        @change="handleStepDataChange"
      />
      
      <!-- 通用步骤表单（其他步骤） -->
      <div v-else class="generic-step-form">
        <h4>{{ stepName }}</h4>
        <p class="step-description">{{ getStepDescription() }}</p>
        
        <!-- 编辑模式 -->
        <div v-if="!readonly">
          <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
            <el-form-item label="备注说明" prop="remark">
              <el-input
                v-model="form.remark"
                type="textarea"
                :rows="3"
                :placeholder="`请输入${stepName}的备注说明`"
              />
            </el-form-item>
          </el-form>
        </div>
        
        <!-- 查看模式 -->
        <div v-else class="view-content">
          <div class="info-item">
            <label>备注说明：</label>
            <span>{{ form.remark || '暂无备注' }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 通用文件上传区域 -->
    <div class="common-sections">
      <div class="form-section">
        <div class="section-header">
          <span class="section-title">相关材料</span>
        </div>
        <div class="section-content">
          <!-- 查看模式 -->
          <div v-if="readonly">
            <div v-if="form.fileList && form.fileList.length > 0" class="file-view-container">
              <div v-for="(file, index) in form.fileList" :key="index" class="file-item">
                <FileView :file="file" :viewMode="'text'" />
              </div>
            </div>
            <div v-else class="no-data">
              <el-icon class="no-data-icon">
                <FolderOpened />
              </el-icon>
              <span class="no-data-text">暂无文件</span>
            </div>
          </div>
          
          <!-- 编辑模式 -->
          <div v-else class="file-upload-container">
            <FileUpload 
              ref="fileUploadRef" 
              :fileData="{ fileBizType: 'st_material_progress' }"
              @fileLoad="handleFileLoad" 
              :noLimit="true" 
              :limit="10" 
              :viewMode="'text'" 
              :value="form.fileList || []"
              :temp="{ tempType: false, type: 'stMaterialProgress-file', name: '申请进度材料' }" 
            />
          </div>
        </div>
      </div>

      <!-- 通用时间选择 -->
      <div v-if="!readonly" class="form-section">
        <div class="section-header">
          <span class="section-title">{{ getTimeLabel() }}</span>
        </div>
        <div class="section-content">
          <el-form-item :label="getTimeLabel()" prop="selectTime" label-width="0">
            <el-date-picker 
              v-model="form.selectTime" 
              type="datetime" 
              :placeholder="`请选择${getTimeLabel()}`"
              format="YYYY-MM-DD HH:mm:ss" 
              value-format="YYYY-MM-DD HH:mm:ss" 
              :shortcuts="dateShortcuts"
              style="width: 100%" 
            />
          </el-form-item>
        </div>
      </div>

      <!-- 通用备注 -->
      <div v-if="!hasSpecificForm && !readonly" class="form-section">
        <div class="section-header">
          <span class="section-title">备注说明</span>
        </div>
        <div class="section-content">
          <el-form-item label="备注说明" prop="remark" label-width="0">
            <el-input 
              v-model="form.remark" 
              type="textarea" 
              :rows="3" 
              :placeholder="`请输入${stepName}的备注说明`" 
            />
          </el-form-item>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onBeforeUnmount, nextTick, computed } from 'vue'
import { FolderOpened } from '@element-plus/icons-vue'
import { parseTime } from '@/utils/ruoyi'
import FileUpload from '@/components/FileUpload'
import FileView from '@/components/FileView'
import WritingProcessStep from './WritingProcessStep.vue'
import WritingFinalizationStep from './WritingFinalizationStep.vue'
import ApplicationFillingStep from './ApplicationFillingStep.vue'
import ApplicationSubmissionStep from './ApplicationSubmissionStep.vue'
import AdmissionNoticeStep from './AdmissionNoticeStep.vue'
import AccommodationRegistrationStep from './AccommodationRegistrationStep.vue'

const props = defineProps({
  progressData: {
    type: Object,
    required: true
  },
  stepName: {
    type: String,
    default: '当前步骤'
  },
  stepCode: {
    type: String,
    required: true
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['submit', 'dataChange'])

const formRef = ref(null)
const fileUploadRef = ref(null)

// 是否有特定表单组件
const hasSpecificForm = computed(() => {
  return ['WRITING_PROCESS', 'WRITING_FINALIZATION', 'APPLICATION_FILLING', 
          'APPLICATION_SUBMISSION', 'ADMISSION_NOTICE', 'ACCOMMODATION_REGISTRATION'].includes(props.stepCode)
})

// 步骤特定数据
const stepSpecificData = ref({})

// 通用表单数据
const form = reactive({
  fileList: [],
  selectTime: '',
  remark: ''
})

// 时间快捷选项
const dateShortcuts = [
  {
    text: '现在',
    value: new Date()
  },
  {
    text: '今天',
    value: () => {
      const date = new Date()
      date.setHours(0, 0, 0, 0)
      return date
    }
  },
  {
    text: '昨天',
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() - 3600 * 1000 * 24)
      date.setHours(0, 0, 0, 0)
      return date
    }
  }
]

const rules = {
  selectTime: [
    { required: true, message: `请选择${getTimeLabel()}`, trigger: 'change' }
  ],
  remark: [
    { required: true, message: '请输入备注说明', trigger: 'blur' }
  ]
}

// 获取步骤描述
const getStepDescription = () => {
  const descriptions = {
    'INTERVIEW_INVITATION': '部分学校会安排面试，升学老师沟通并安排面培',
    'INTERVIEW_TRAINING': '升学老师沟通并安排面培，模拟面试练习',
    'ATTEND_INTERVIEW': '学生参加学校面试，面试成功则收到录取',
    'ACCEPT_OFFER_DEPOSIT': '录取后接受入读并缴纳留位费',
    'VISA_PREPARATION': '缴纳留位费后，进入签证申请环节',
    'SCHOOL_VISA_UPLOAD': '部分学校要求上传资料至学校签证系统',
    'CONDITIONAL_MATERIALS': '获有条件录取的学生，需递交满足条件的学术材料等',
    'VISA_FORMAL_OFFER': '签证审批通过且所有条件满足后，获得正式录取'
  }
  return descriptions[props.stepCode] || `${props.stepName}相关操作`
}

// 获取时间标签
const getTimeLabel = () => {
  const labels = {
    'WRITING_PROCESS': '文书约谈时间',
    'WRITING_FINALIZATION': '文书定稿时间',
    'APPLICATION_FILLING': '申请填写时间',
    'APPLICATION_SUBMISSION': '申请递交时间',
    'INTERVIEW_INVITATION': '面试邀请时间',
    'INTERVIEW_TRAINING': '面试培训时间',
    'ATTEND_INTERVIEW': '面试时间',
    'ADMISSION_NOTICE': '录取通知时间',
    'ACCEPT_OFFER_DEPOSIT': '留位费时间',
    'VISA_PREPARATION': '签证准备时间',
    'SCHOOL_VISA_UPLOAD': '签证上传时间',
    'CONDITIONAL_MATERIALS': '材料递交时间',
    'VISA_FORMAL_OFFER': '正式录取时间',
    'ACCOMMODATION_REGISTRATION': '注册时间'
  }
  return labels[props.stepCode] || '操作时间'
}

// 处理文件上传
const handleFileLoad = (data) => {
  form.fileList = data.fileList || []
}

// 处理步骤特定数据变化
const handleStepDataChange = (data) => {
  stepSpecificData.value = data
  emitDataChange()
}

// 发出数据变化事件
const emitDataChange = () => {
  const allData = {
    ...form,
    stepSpecificData: stepSpecificData.value
  }
  emit('dataChange', allData)
}

// 获取表单数据
const getFormData = () => {
  return {
    fileList: form.fileList,
    selectTime: form.selectTime,
    remark: form.remark,
    stepSpecificData: stepSpecificData.value,
    operationData: JSON.stringify({
      fileList: form.fileList,
      stepSpecificData: stepSpecificData.value
    })
  }
}

// 验证表单
const validateForm = async () => {
  try {
    // 验证通用表单
    if (formRef.value) {
      await formRef.value.validate()
    }
    
    // 验证步骤特定表单
    if (hasSpecificForm.value) {
      // 这里可以调用特定步骤组件的验证方法
      // 暂时返回true
    }
    
    return true
  } catch (error) {
    return false
  }
}

// 重置表单
const resetForm = () => {
  form.fileList = []
  form.selectTime = ''
  form.remark = ''
  stepSpecificData.value = {}
  
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 初始化数据
const initData = () => {
  // 从progressData加载步骤特定数据
  if (props.progressData) {
    const fieldMap = getStepDataFieldMap()
    if (fieldMap.data) {
      try {
        const data = JSON.parse(props.progressData[fieldMap.data] || '{}')
        stepSpecificData.value = data.stepSpecificData || {}
        form.fileList = data.fileList || []
      } catch (error) {
        stepSpecificData.value = {}
        form.fileList = []
      }
    }
    
    form.selectTime = props.progressData[fieldMap.time] || ''
    form.remark = props.progressData[fieldMap.remark] || ''
  }
}

// 获取步骤数据字段映射
const getStepDataFieldMap = () => {
  const maps = {
    'WRITING_PROCESS': {
      remark: 'stepWritingProcessRemark',
      time: 'stepWritingProcessSelectTime',
      data: 'stepWritingProcessData'
    },
    'WRITING_FINALIZATION': {
      remark: 'stepWritingFinalizationRemark',
      time: 'stepWritingFinalizationSelectTime',
      data: 'stepWritingFinalizationData'
    },
    'APPLICATION_FILLING': {
      remark: 'stepApplicationFillingRemark',
      time: 'stepApplicationFillingSelectTime',
      data: 'stepApplicationFillingData'
    },
    'APPLICATION_SUBMISSION': {
      remark: 'stepApplicationSubmissionRemark',
      time: 'stepApplicationSubmissionSelectTime',
      data: 'stepApplicationSubmissionData'
    },
    'ADMISSION_NOTICE': {
      remark: 'stepAdmissionNoticeRemark',
      time: 'stepAdmissionNoticeSelectTime',
      data: 'stepAdmissionNoticeData'
    },
    'ACCOMMODATION_REGISTRATION': {
      remark: 'stepAccommodationRegistrationRemark',
      time: 'stepAccommodationRegistrationSelectTime',
      data: 'stepAccommodationRegistrationData'
    }
  }
  return maps[props.stepCode] || { remark: '', time: '', data: '' }
}

// 监听数据变化
watch(() => form, (newData) => {
  emitDataChange()
}, { deep: true })

// 监听步骤代码变化
watch(() => props.stepCode, () => {
  initData()
}, { immediate: true })

// 组件销毁前的清理
onBeforeUnmount(() => {
  if (formRef.value) {
    formRef.value = null
  }
  if (fileUploadRef.value) {
    fileUploadRef.value = null
  }
})

// 暴露方法
defineExpose({
  getFormData,
  resetForm,
  validateForm
})
</script>

<style scoped lang="scss">
.unified-step-form {
  .step-specific-form {
    margin-bottom: 20px;
  }

  .generic-step-form {
    h4 {
      margin-bottom: 12px;
      color: #303133;
      font-weight: 600;
    }

    .step-description {
      margin-bottom: 16px;
      color: #606266;
      font-size: 14px;
      line-height: 1.5;
    }

    .view-content {
      .info-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;

        label {
          min-width: 100px;
          font-weight: 500;
          color: #606266;
        }

        span {
          color: #303133;
        }
      }
    }
  }

  .common-sections {
    .form-section {
      margin-bottom: 24px;

      .section-header {
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #ebeef5;

        .section-title {
          font-size: 14px;
          font-weight: 600;
          color: #303133;
        }
      }

      .section-content {
        padding-left: 12px;

        .file-upload-container {
          width: 100%;
          border: 1px dashed #dcdfe6;
          border-radius: 6px;
          padding: 10px;
        }

        .file-view-container {
          .file-item {
            margin-bottom: 8px;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }

        .no-data {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 20px;
          color: #909399;
          background-color: #fafafa;
          border: 1px dashed #dcdfe6;
          border-radius: 4px;
          min-height: 60px;

          .no-data-icon {
            font-size: 18px;
            margin-right: 8px;
            opacity: 0.6;
          }

          .no-data-text {
            font-size: 13px;
          }
        }
      }
    }
  }
}
</style> 