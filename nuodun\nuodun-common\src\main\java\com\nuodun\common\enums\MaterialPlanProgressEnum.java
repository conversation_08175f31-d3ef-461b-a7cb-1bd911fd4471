package com.nuodun.common.enums;

/**
 * 专业申请流程进度相关枚举类
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
public class MaterialPlanProgressEnum {

    /**
     * 申请流程步骤枚举 - 新的14步骤流程
     * 分为4个阶段：申请准备阶段、申请后环节、录取后流程、最终确认阶段
     */
    public enum StepCode {
        // 第一阶段：申请准备阶段
        /**
         * 文书环节
         */
        WRITING_PROCESS("WRITING_PROCESS", "文书环节"),

        /**
         * 文书定稿
         */
        WRITING_FINALIZATION("WRITING_FINALIZATION", "文书定稿"),

        /**
         * 学校申请填写
         */
        APPLICATION_FILLING("APPLICATION_FILLING", "学校申请填写"),

        /**
         * 申请递交确认
         */
        APPLICATION_SUBMISSION("APPLICATION_SUBMISSION", "申请递交确认"),

        // 第二阶段：申请后环节
        /**
         * 面试邀请通知
         */
        INTERVIEW_INVITATION("INTERVIEW_INVITATION", "面试邀请通知"),

        /**
         * 面试培训安排
         */
        INTERVIEW_TRAINING("INTERVIEW_TRAINING", "面试培训安排"),

        /**
         * 参加面试
         */
        ATTEND_INTERVIEW("ATTEND_INTERVIEW", "参加面试"),

        /**
         * 录取通知
         */
        ADMISSION_NOTICE("ADMISSION_NOTICE", "录取通知"),

        // 第三阶段：录取后流程
        /**
         * 接受录取&留位费
         */
        ACCEPT_OFFER_DEPOSIT("ACCEPT_OFFER_DEPOSIT", "接受录取&留位费"),

        /**
         * 签证申请准备
         */
        VISA_PREPARATION("VISA_PREPARATION", "签证申请准备"),

        /**
         * 学校签证系统上传
         */
        SCHOOL_VISA_UPLOAD("SCHOOL_VISA_UPLOAD", "学校签证系统上传"),

        // 第四阶段：最终确认阶段
        /**
         * 条件录取材料递交
         */
        CONDITIONAL_MATERIALS("CONDITIONAL_MATERIALS", "条件录取材料递交"),

        /**
         * 签证审批&正式录取
         */
        VISA_FORMAL_OFFER("VISA_FORMAL_OFFER", "签证审批&正式录取"),

        /**
         * 住宿申请&注册
         */
        ACCOMMODATION_REGISTRATION("ACCOMMODATION_REGISTRATION", "住宿申请&注册");

        private final String code;
        private final String name;

        StepCode(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        /**
         * 根据编码获取枚举
         */
        public static StepCode getByCode(String code) {
            for (StepCode stepCode : values()) {
                if (stepCode.getCode().equals(code)) {
                    return stepCode;
                }
            }
            return null;
        }

        /**
         * 根据编码获取枚举 - 简化方法名
         */
        public static StepCode get(String code) {
            return getByCode(code);
        }

        /**
         * 获取下一个步骤 - 新的14步骤流程
         */
        public StepCode getNext() {
            switch (this) {
                // 第一阶段：申请准备阶段
                case WRITING_PROCESS: return WRITING_FINALIZATION;
                case WRITING_FINALIZATION: return APPLICATION_FILLING;
                case APPLICATION_FILLING: return APPLICATION_SUBMISSION;
                case APPLICATION_SUBMISSION: return INTERVIEW_INVITATION;
                
                // 第二阶段：申请后环节
                case INTERVIEW_INVITATION: return INTERVIEW_TRAINING;
                case INTERVIEW_TRAINING: return ATTEND_INTERVIEW;
                case ATTEND_INTERVIEW: return ADMISSION_NOTICE;
                case ADMISSION_NOTICE: return ACCEPT_OFFER_DEPOSIT;
                
                // 第三阶段：录取后流程
                case ACCEPT_OFFER_DEPOSIT: return VISA_PREPARATION;
                case VISA_PREPARATION: return SCHOOL_VISA_UPLOAD;
                case SCHOOL_VISA_UPLOAD: return CONDITIONAL_MATERIALS;
                
                // 第四阶段：最终确认阶段
                case CONDITIONAL_MATERIALS: return VISA_FORMAL_OFFER;
                case VISA_FORMAL_OFFER: return ACCOMMODATION_REGISTRATION;
                case ACCOMMODATION_REGISTRATION: return null; // 最后一步
                
                default: return null;
            }
        }
    }

    /**
     * 步骤状态枚举
     */
    public enum StepStatus {
        /**
         * 待处理
         */
        PENDING("PENDING", "待处理"),
        
        /**
         * 进行中
         */
        IN_PROGRESS("IN_PROGRESS", "进行中"),
        
        /**
         * 已完成
         */
        COMPLETED("COMPLETED", "已完成"),
        
        /**
         * 已跳过
         */
        SKIPPED("SKIPPED", "已跳过"),
        
        /**
         * 已取消
         */
        CANCELLED("CANCELLED", "已取消");

        private final String code;
        private final String name;

        StepStatus(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        /**
         * 根据编码获取枚举
         */
        public static StepStatus getByCode(String code) {
            for (StepStatus status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }

        /**
         * 根据编码获取枚举 - 简化方法名
         */
        public static StepStatus get(String code) {
            return getByCode(code);
        }
    }

    /**
     * 整体进度状态枚举
     */
    public enum ProgressStatus {
        /**
         * 未开始
         */
        NOT_STARTED("NOT_STARTED", "未开始"),
        
        /**
         * 进行中
         */
        IN_PROGRESS("IN_PROGRESS", "进行中"),
        
        /**
         * 已完成
         */
        COMPLETED("COMPLETED", "已完成"),
        
        /**
         * 暂停
         */
        SUSPENDED("SUSPENDED", "暂停"),
        
        /**
         * 取消
         */
        CANCELLED("CANCELLED", "取消");

        private final String code;
        private final String name;

        ProgressStatus(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        /**
         * 根据编码获取枚举
         */
        public static ProgressStatus getByCode(String code) {
            for (ProgressStatus status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }

    /**
     * 操作类型枚举
     */
    public enum OperationType {
        /**
         * 开始
         */
        START("START", "开始"),
        
        /**
         * 完成
         */
        COMPLETE("COMPLETE", "完成"),
        
        /**
         * 回退
         */
        ROLLBACK("ROLLBACK", "回退"),
        
        /**
         * 更新
         */
        UPDATE("UPDATE", "更新"),
        
        /**
         * 取消
         */
        CANCEL("CANCEL", "取消"),
        
        /**
         * 跳过
         */
        SKIP("SKIP", "跳过"),
        
        /**
         * 初始化
         */
        INIT("INIT", "初始化");

        private final String code;
        private final String name;

        OperationType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        /**
         * 根据编码获取枚举
         */
        public static OperationType getByCode(String code) {
            for (OperationType type : values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 操作人类型枚举
     */
    public enum OperatorType {
        /**
         * 手动操作
         */
        MANUAL("MANUAL", "手动操作"),
        
        /**
         * 自动操作
         */
        AUTO("AUTO", "自动操作"),
        
        /**
         * 系统操作
         */
        SYSTEM("SYSTEM", "系统操作");

        private final String code;
        private final String name;

        OperatorType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        /**
         * 根据编码获取枚举
         */
        public static OperatorType getByCode(String code) {
            for (OperatorType type : values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * Offer类型枚举
     */
    public enum OfferType {
        /**
         * 有条件录取
         */
        CONDITIONAL("CONDITIONAL", "有条件录取"),
        
        /**
         * 无条件录取
         */
        UNCONDITIONAL("UNCONDITIONAL", "无条件录取");

        private final String code;
        private final String name;

        OfferType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        /**
         * 根据编码获取枚举
         */
        public static OfferType getByCode(String code) {
            for (OfferType type : values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }
}
