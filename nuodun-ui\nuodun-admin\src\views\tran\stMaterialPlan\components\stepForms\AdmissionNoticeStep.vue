<template>
  <div class="admission-notice-step">
    <!-- 查看模式 -->
    <div v-if="readonly" class="view-mode">
      <div class="step-info">
        <h4>录取通知详情</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>录取状态：</label>
            <el-tag :type="stepData.admissionReceived ? 'success' : 'danger'">
              {{ stepData.admissionReceived ? '已收到录取通知' : '未收到录取通知' }}
            </el-tag>
          </div>
          <template v-if="stepData.admissionReceived">
            <div class="info-item">
              <label>录取类型：</label>
              <el-tag :type="stepData.admissionType === 'CONDITIONAL' ? 'warning' : 'success'">
                {{ stepData.admissionType === 'CONDITIONAL' ? '有条件录取' : '无条件录取' }}
              </el-tag>
            </div>
            <div class="info-item">
              <label>录取通知时间：</label>
              <span>{{ parseTime(stepData.noticeTime) || '暂无' }}</span>
            </div>
            <div v-if="stepData.admissionType === 'CONDITIONAL' && stepData.admissionConditions" class="info-item">
              <label>录取条件：</label>
              <span>{{ stepData.admissionConditions }}</span>
            </div>
          </template>
        </div>
      </div>
    </div>

    <!-- 编辑模式 -->
    <div v-else class="edit-mode">
      <el-form :model="stepData" :rules="rules" ref="formRef" label-width="140px">
        <el-form-item label="录取状态" prop="admissionReceived">
          <el-radio-group v-model="stepData.admissionReceived">
            <el-radio :label="true">已收到录取通知</el-radio>
            <el-radio :label="false">未收到录取通知</el-radio>
          </el-radio-group>
          <div class="form-tip">
            <el-text type="warning" size="small">
              注意：如选择"未收到录取通知"，系统将自动结束整个申请流程
            </el-text>
          </div>
        </el-form-item>

        <template v-if="stepData.admissionReceived">
          <el-form-item label="录取类型" prop="admissionType">
            <el-radio-group v-model="stepData.admissionType">
              <el-radio label="CONDITIONAL">有条件录取</el-radio>
              <el-radio label="UNCONDITIONAL">无条件录取</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="录取通知时间" prop="noticeTime">
            <el-date-picker
              v-model="stepData.noticeTime"
              type="datetime"
              placeholder="选择录取通知时间"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>

          <el-form-item 
            v-if="stepData.admissionType === 'CONDITIONAL'" 
            label="录取条件" 
            prop="admissionConditions"
          >
            <el-input
              v-model="stepData.admissionConditions"
              type="textarea"
              :rows="4"
              placeholder="请详细描述需要满足的录取条件，如语言成绩要求、学术成绩要求等"
            />
          </el-form-item>
        </template>

        <el-form-item label="备注说明" prop="remark">
          <el-input
            v-model="stepData.remark"
            type="textarea"
            :rows="3"
            placeholder="请描述录取通知的相关情况"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { parseTime } from '@/utils/ruoyi'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const formRef = ref(null)

// 步骤数据
const stepData = reactive({
  admissionReceived: null,
  admissionType: 'CONDITIONAL',
  noticeTime: '',
  admissionConditions: '',
  remark: ''
})

// 验证规则
const rules = {
  admissionReceived: [
    { required: true, message: '请选择录取状态', trigger: 'change' }
  ],
  admissionType: [
    { required: true, message: '请选择录取类型', trigger: 'change', validator: (rule, value, callback) => {
      if (stepData.admissionReceived && !value) {
        callback(new Error('请选择录取类型'))
      } else {
        callback()
      }
    }}
  ],
  noticeTime: [
    { required: true, message: '请选择录取通知时间', trigger: 'change', validator: (rule, value, callback) => {
      if (stepData.admissionReceived && !value) {
        callback(new Error('请选择录取通知时间'))
      } else {
        callback()
      }
    }}
  ],
  admissionConditions: [
    { required: true, message: '请输入录取条件', trigger: 'blur', validator: (rule, value, callback) => {
      if (stepData.admissionReceived && stepData.admissionType === 'CONDITIONAL' && !value) {
        callback(new Error('请输入录取条件'))
      } else {
        callback()
      }
    }}
  ]
}

// 初始化数据
const initData = () => {
  Object.assign(stepData, {
    admissionReceived: null,
    admissionType: 'CONDITIONAL',
    noticeTime: '',
    admissionConditions: '',
    remark: '',
    ...props.modelValue
  })
}

// 监听数据变化
watch(() => stepData, (newData) => {
  emit('update:modelValue', { ...newData })
  emit('change', { ...newData })
}, { deep: true })

// 监听props变化
watch(() => props.modelValue, () => {
  initData()
}, { immediate: true })

// 验证表单
const validate = async () => {
  if (!formRef.value) return true
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    return false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(stepData, {
    admissionReceived: null,
    admissionType: 'CONDITIONAL',
    noticeTime: '',
    admissionConditions: '',
    remark: ''
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

defineExpose({
  validate,
  resetForm
})
</script>

<style scoped lang="scss">
.admission-notice-step {
  .view-mode {
    .step-info {
      h4 {
        margin-bottom: 16px;
        color: #303133;
        font-weight: 600;
      }

      .info-grid {
        display: grid;
        gap: 12px;

        .info-item {
          display: flex;
          align-items: flex-start;

          label {
            min-width: 120px;
            font-weight: 500;
            color: #606266;
          }

          span {
            color: #303133;
            word-break: break-all;
          }
        }
      }
    }
  }

  .edit-mode {
    .el-form {
      max-width: 600px;
    }

    .form-tip {
      margin-top: 8px;
      padding: 8px 12px;
      background: #fef0f0;
      border: 1px solid #fde2e2;
      border-radius: 4px;
    }
  }
}
</style> 