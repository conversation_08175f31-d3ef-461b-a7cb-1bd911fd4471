<template>
  <div class="writing-finalization-step">
    <!-- 查看模式 -->
    <div v-if="readonly" class="view-mode">
      <div class="step-info">
        <h4>文书定稿详情</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>文书版本号：</label>
            <span>{{ stepData.versionNumber || '暂未设置' }}</span>
          </div>
          <div class="info-item">
            <label>客户确认状态：</label>
            <el-tag :type="stepData.customerConfirmed ? 'success' : 'warning'">
              {{ stepData.customerConfirmed ? '已确认' : '待确认' }}
            </el-tag>
          </div>
          <div v-if="stepData.customerConfirmed" class="info-item">
            <label>确认时间：</label>
            <span>{{ parseTime(stepData.confirmTime) || '暂无' }}</span>
          </div>
          <div class="info-item">
            <label>文书内容说明：</label>
            <span>{{ stepData.contentDescription || '暂无说明' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑模式 -->
    <div v-else class="edit-mode">
      <el-form :model="stepData" :rules="rules" ref="formRef" label-width="140px">
        <el-form-item label="文书版本号" prop="versionNumber">
          <el-input v-model="stepData.versionNumber" placeholder="如：V1.0、V2.0等" />
        </el-form-item>

        <el-form-item label="客户确认状态" prop="customerConfirmed">
          <el-radio-group v-model="stepData.customerConfirmed">
            <el-radio :label="true">已确认</el-radio>
            <el-radio :label="false">待确认</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="stepData.customerConfirmed" label="确认时间" prop="confirmTime">
          <el-date-picker
            v-model="stepData.confirmTime"
            type="datetime"
            placeholder="选择确认时间"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>

        <el-form-item label="文书内容说明" prop="contentDescription">
          <el-input
            v-model="stepData.contentDescription"
            type="textarea"
            :rows="4"
            placeholder="请描述文书的主要内容和特点"
          />
        </el-form-item>

        <el-form-item label="备注说明" prop="remark">
          <el-input
            v-model="stepData.remark"
            type="textarea"
            :rows="2"
            placeholder="其他备注信息"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { parseTime } from '@/utils/ruoyi'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const formRef = ref(null)

// 步骤数据
const stepData = reactive({
  versionNumber: '',
  customerConfirmed: false,
  confirmTime: '',
  contentDescription: '',
  remark: ''
})

// 验证规则
const rules = {
  versionNumber: [
    { required: true, message: '请输入文书版本号', trigger: 'blur' }
  ],
  customerConfirmed: [
    { required: true, message: '请选择客户确认状态', trigger: 'change' }
  ],
  confirmTime: [
    { required: true, message: '请选择确认时间', trigger: 'change', validator: (rule, value, callback) => {
      if (stepData.customerConfirmed && !value) {
        callback(new Error('请选择确认时间'))
      } else {
        callback()
      }
    }}
  ]
}

// 初始化数据
const initData = () => {
  Object.assign(stepData, {
    versionNumber: '',
    customerConfirmed: false,
    confirmTime: '',
    contentDescription: '',
    remark: '',
    ...props.modelValue
  })
}

// 监听数据变化
watch(() => stepData, (newData) => {
  emit('update:modelValue', { ...newData })
  emit('change', { ...newData })
}, { deep: true })

// 监听props变化
watch(() => props.modelValue, () => {
  initData()
}, { immediate: true })

// 验证表单
const validate = async () => {
  if (!formRef.value) return true
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    return false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(stepData, {
    versionNumber: '',
    customerConfirmed: false,
    confirmTime: '',
    contentDescription: '',
    remark: ''
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

defineExpose({
  validate,
  resetForm
})
</script>

<style scoped lang="scss">
.writing-finalization-step {
  .view-mode {
    .step-info {
      h4 {
        margin-bottom: 16px;
        color: #303133;
        font-weight: 600;
      }

      .info-grid {
        display: grid;
        gap: 12px;

        .info-item {
          display: flex;
          align-items: flex-start;

          label {
            min-width: 120px;
            font-weight: 500;
            color: #606266;
          }

          span {
            color: #303133;
            word-break: break-all;
          }
        }
      }
    }
  }

  .edit-mode {
    .el-form {
      max-width: 600px;
    }
  }
}
</style> 